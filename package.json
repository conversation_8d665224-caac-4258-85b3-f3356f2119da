{"name": "my-video", "version": "1.0.0", "description": "A Remotion template for TikTok-style captions", "repository": {}, "license": "UNLICENSED", "private": true, "dependencies": {"@remotion/cli": "4.0.314", "@remotion/zod-types": "4.0.314", "@remotion/animation-utils": "4.0.314", "@remotion/layout-utils": "4.0.314", "@remotion/media-utils": "4.0.314", "react": "19.0.0", "react-dom": "19.0.0", "remotion": "4.0.314", "zod": "3.22.3", "@remotion/tailwind-v4": "4.0.314", "tailwindcss": "4.0.0"}, "devDependencies": {"@remotion/eslint-config-flat": "4.0.314", "@remotion/install-whisper-cpp": "4.0.314", "@remotion/captions": "4.0.314", "@types/react": "19.0.0", "@types/web": "0.0.166", "eslint": "9.19.0", "prettier": "3.3.3", "typescript": "5.8.2"}, "scripts": {"dev": "remotion studio", "build": "remotion bundle", "upgrade": "remotion upgrade", "lint": "eslint src && tsc", "create-subtitles": "node sub.mjs"}, "sideEffects": ["*.css"]}